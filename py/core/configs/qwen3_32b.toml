[completion]
provider = "litellm"
concurrent_request_limit = 64
request_timeout = 120

  [completion.generation_config]
  model = "qwen/qwen3-32b"
  temperature = 0.1
  top_p = 1
  max_tokens_to_sample = 4_096
  stream = false
  add_generation_kwargs = { }

[ingestion]
provider = "unstructured_local"
strategy = "auto"
chunking_strategy = "by_title"
new_after_n_chars = 2_048
max_characters = 4_096
combine_under_n_chars = 1_024
overlap = 1_024

    [ingestion.extra_parsers]
    pdf = ["zerox", "ocr"]

[orchestration]
provider = "hatchet"
kg_creation_concurrency_limit = 32
ingestion_concurrency_limit = 16
kg_concurrency_limit = 8

[embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256

[completion_embedding]
provider = "litellm"
base_model = "openai/text-embedding-3-small"
base_dimension = 512
batch_size = 128
concurrent_request_limit = 256
